

# 本地AI检索系统技术方案

## 目录

1. [技术调研总结](#1-技术调研总结)
   - 1.1 现有技术分析
   - 1.2 技术方案定位
   - 1.3 核心设计理念
   - 1.4 智能路由创新
2. [系统架构总览](#2-系统架构总览)
   - 2.1 整体架构设计
   - 2.2 技术架构特点
   - 2.3 核心模块
     - 2.3.1 智能路由（意图识别）
     - 2.3.2 文档处理与内容抽取
     - 2.3.3 索引子系统
       - 2.3.3.1 元数据索引
       - 2.3.3.2 全文索引
       - 2.3.3.3 语义索引
     - 2.3.4 检索、融合与排序
3. [项目目录结构](#3-项目目录结构)
   - 3.1 FastAPI + Streamlit分离架构
   - 3.2 目录设计说明
4. [分阶段架构设计](#4-分阶段架构设计)
   - 4.1 第一阶段架构（MVP - 5-6周）
   - 4.2 第二阶段架构（智能化 - 4-5周）
   - 4.3 第三阶段架构（优化完善 - 3-4周）
5. [分阶段落地开发计划](#5-分阶段落地开发计划)
   - 5.1 第一阶段开发计划
     - 5.1.1 环境搭建与基础架构（第1周）
     - 5.1.2 元数据索引开发（第2-3周）
     - 5.1.3 文档解析器开发（第3-4周）
     - 5.1.4 全文索引实现（第4-5周）
     - 5.1.5 FastAPI后端开发（第5周）
     - 5.1.6 Streamlit前端开发（第6周）
   - 5.2 第二阶段开发计划
     - 5.2.1 语义搜索引擎（第7-9周）
     - 5.2.2 智能路由系统（第9-10周）
     - 5.2.3 结果融合算法（第10-11周）
   - 5.3 第三阶段开发计划
     - 5.3.1 性能优化（第12-13周）
     - 5.3.2 用户体验优化（第13-14周）
     - 5.3.3 系统集成测试（第14-15周）
6. [后期发展计划](#6-后期发展计划)
   - 6.1 技术演进路线图
   - 6.2 功能扩展规划
     - 6.2.1 多模态搜索能力
     - 6.2.2 协作搜索功能
     - 6.2.3 企业级功能
   - 6.3 技术债务管理
   - 6.4 开源社区建设
   - 6.5 商业化路径
   - 6.6 风险评估与应对
   - 6.7 成功指标定义
7. [总结与展望](#7-总结与展望)
   - 7.1 项目价值总结
   - 7.2 技术发展趋势
   - 7.3 项目展望
8. [附录](#附录)
   - A. 技术选型对比
   - B. 性能基准测试
   - C. 部署配置模板
   - D. 监控和日志配置
   - E. 参考资料

---

## 1. 技术调研总结

### 1.1 现有技术分析

基于对Everything、福昕AI、Windows Search等技术的深度调研：

#### 📊 **技术方案对比分析**

| 技术方案 | 优势 | 缺陷 | 适用场景 |
|----------|------|------|----------|
| **Everything** | 极速文件名搜索(<5ms) | 仅支持文件名，无内容搜索 | 文件定位 |
| **Windows Search** | 功能全面，系统集成 | 慢速索引，资源占用高 | 系统默认搜索 |
| **福昕AI** | 强大语义理解，PDF专业 | 仅限PDF，无通用性 | PDF文档处理 |

#### 🚀 **超越现有技术的突破**

| 对比维度 | Everything | Windows Search | 福昕AI | 本方案 |
|----------|------------|----------------|--------|--------|
| **文件名搜索** | ⭐⭐⭐⭐⭐ 极速 | ⭐⭐ 较慢 | ❌ 不支持 | ⭐⭐⭐⭐⭐ 极速+智能 |
| **内容搜索** | ❌ 不支持 | ⭐⭐⭐ 支持但慢 | ⭐⭐⭐⭐ PDF专用 | ⭐⭐⭐⭐⭐ 全格式支持 |
| **语义理解** | ❌ 不支持 | ❌ 不支持 | ⭐⭐⭐⭐⭐ 强大 | ⭐⭐⭐⭐⭐ 本地化 |
| **资源占用** | ⭐⭐⭐⭐ 很低 | ⭐⭐ 高 | ⭐⭐⭐ 中等 | ⭐⭐⭐⭐⭐ 极低 |
| **配置复杂度** | ⭐⭐⭐ 简单 | ⭐ 复杂 | ⭐⭐⭐⭐ 简单 | ⭐⭐⭐⭐⭐ 零配置 |

#### 🚀 **关键技术调研分析说明**

| 技术组件 | 选择方案 | 调研依据 | 替代方案 |
|----------|----------|----------|----------|
| **元数据索引** | Everything SDK + NTFS MFT | 极速性能，支持全文件类型 | 自研文件系统扫描 |
| **全文索引** | Whoosh + BM25 | Python生态，易于集成 | Elasticsearch, Lucene |
| **语义模型** | BGE中文模型 | 中文优化，本地部署 | OpenAI Embeddings |
| **向量数据库** | FAISS/Milvus/Weaviate | 配置可切换，性能优秀 | ChromaDB, Pinecone |
| **文档解析** | pdfminer + python-docx | 成熟稳定，格式支持全 | PyPDF2, docx2txt |
| **RAG框架** | Langchain+LangIndex |  |  |

### 1.2 技术方案定位

**目标**：结合Everything的极速性能 + 福昕AI的智能理解 + 现代化技术栈

**策略**：
- 保持Everything级别的文件名搜索性能
- 扩展全文检索能力到常用文档格式
- 集成语义搜索实现智能理解
- 采用现代化架构确保可扩展性

### 1.3 核心设计理念

1. **分阶段实施**：MVP优先，逐步增强智能化能力
2. **智能路由**：根据查询意图自动选择最优检索策略
3. **技术栈统一**：Python生态，FastAPI + Streamlit架构
4. **部署简化**：Conda虚拟环境，无Docker依赖

### 1.4 智能路由创新

#### 🧠 **自动意图识别**

1. **查询类型智能判断**：
   - 短关键词 → 元数据索引（<5ms）
   - 内容关键词 → 全文索引（<50ms）
   - 自然语言 → 语义索引（<200ms）

2. **混合检索融合**：
   - 加权BM25分数 + 向量相似度
   - 权重可配置调整
   - 智能结果排序

3. **零配置体验**：
   - 自动文件发现和索引
   - 智能查询理解
   - 自适应性能优化

---

## 2. 系统架构总览

### 2.1 整体架构设计

```mermaid
graph TB
    subgraph "前端展示层"
        STREAMLIT[🖥️ Streamlit Web界面<br/>用户交互 + 结果展示<br/>搜索框 + 过滤器 + 结果列表]
    end

    subgraph "API服务层"
        FASTAPI[🚀 FastAPI后端服务<br/>RESTful API + 业务逻辑<br/>异步处理 + 错误处理]
    end

    subgraph "智能路由层"
        INTENT[🎯 意图识别引擎<br/>查询分析 + 策略选择<br/>规则匹配 + ML分类]
        ROUTER[🧠 智能路由器<br/>引擎调度 + 并行处理<br/>负载均衡 + 超时控制]
    end

    subgraph "搜索结果层"
        FUSION[🔄 结果融合器<br/>多源结果合并<br/>RRF算法 + 重排序]
        RANKING[📊 智能排序<br/>相关性计算<br/>个性化排序]
        HIGHLIGHT[💡 结果增强<br/>关键词高亮<br/>摘要生成]
    end

    subgraph "三模式检索引擎"
        META[⚡ 元数据引擎<br/>文件名/路径/属性<br/>支持所有文件类型<br/>SQLite + 索引优化]
        FULL[📚 全文引擎<br/>内容关键词搜索<br/>仅文本文档<br/>Whoosh + BM25]
        SEMANTIC[🧠 语义引擎<br/>智能语义理解<br/>仅文本文档<br/>BGE + FAISS]
    end

    subgraph "数据处理层"
        PARSER[📄 文档解析器<br/>多格式内容提取<br/>PDF/Office/TXT/MD]
        MONITOR[👁️ 文件监控<br/>实时索引更新<br/>增量处理]
        EXTRACTOR[🔍 特征提取<br/>文本预处理<br/>向量化]
    end

    subgraph "存储层"
        SQLITE[🗄️ SQLite数据库<br/>元数据存储<br/>文件属性索引]
        WHOOSH[📊 Whoosh索引<br/>全文检索索引<br/>倒排索引]
        VECTOR[🔢 向量存储<br/>语义嵌入向量<br/>FAISS索引]
    end

    STREAMLIT <--> FASTAPI
    FASTAPI --> INTENT
    INTENT --> ROUTER
    ROUTER --> META
    ROUTER --> FULL
    ROUTER --> SEMANTIC

    META --> FUSION
    FULL --> FUSION
    SEMANTIC --> FUSION

    FUSION --> RANKING
    RANKING --> HIGHLIGHT
    HIGHLIGHT --> FASTAPI

    META --> SQLITE
    FULL --> WHOOSH
    SEMANTIC --> VECTOR

    PARSER --> WHOOSH
    PARSER --> EXTRACTOR
    EXTRACTOR --> VECTOR
    MONITOR --> SQLITE

    style FASTAPI fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style INTENT fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style FUSION fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style META fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style FULL fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style SEMANTIC fill:#fce4ec,stroke:#c2185b,stroke-width:2px
```

### 2.2 技术架构特点

| 架构层级 | 技术选型 | 核心功能 | 设计原则 |
|----------|----------|----------|----------|
| **前端层** | Streamlit | 用户交互界面 | 简洁高效，Python原生 |
| **API层** | FastAPI | 服务接口 | 高性能，自动文档生成 |
| **路由层** | Python | 智能调度 | 意图识别，策略优化 |
| **引擎层** | SQLite+Whoosh+FAISS | 多模式检索 | 性能优先，功能互补 |
| **存储层** | 文件系统+数据库 | 数据持久化 | 轻量级，易维护 |

---

### 2.3 核心模块

#### 2.3.1 智能路由（意图识别）

```mermaid
flowchart TD
    START([用户输入查询]) --> PREPROCESS[查询预处理<br/>去除特殊字符<br/>分词处理]

    PREPROCESS --> INTENT_ANALYSIS{意图分析}

    INTENT_ANALYSIS -->|文件名模式<br/>report.pdf<br/>*.jpg<br/>2023年报告| METADATA_ROUTE[路由到元数据检索<br/>支持所有文件类型<br/>包括音频/视频/图像]

    INTENT_ANALYSIS -->|内容关键词<br/>包含合同的文档<br/>提到张三的文件<br/>关于项目的资料| FULLTEXT_ROUTE[路由到全文检索<br/>仅限文本文档<br/>PDF/Office/MD/TXT]

    INTENT_ANALYSIS -->|自然语言<br/>去年的工作总结<br/>类似这个报告的文档<br/>关于AI的研究资料| SEMANTIC_ROUTE[路由到语义检索<br/>仅限文本文档<br/>PDF/Office/MD/TXT]

    METADATA_ROUTE --> META_SEARCH[⚡ 元数据搜索<br/>文件名/路径/时间/大小<br/>所有文件类型<br/>响应时间小于5ms]

    FULLTEXT_ROUTE --> FULL_SEARCH[📚 全文搜索<br/>BM25关键词匹配<br/>文本文档内容<br/>响应时间小于50ms]

    SEMANTIC_ROUTE --> SEM_SEARCH[🧠 语义搜索<br/>向量相似度计算<br/>文档语义理解<br/>响应时间小于200ms]

    META_SEARCH --> RESULT_FUSION[🔄 结果融合与展示]
    FULL_SEARCH --> RESULT_FUSION
    SEM_SEARCH --> RESULT_FUSION

    RESULT_FUSION --> FINAL_DISPLAY[📋 统一文件展示界面<br/>文件列表加预览加操作]

    style INTENT_ANALYSIS fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style META_SEARCH fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style FULL_SEARCH fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style SEM_SEARCH fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
```

#### 2.3.2 文档处理与内容抽取

```mermaid
flowchart TD
    START([文件变化事件]) --> DETECT[文件类型检测<br/>MIME类型识别<br/>扩展名验证]

    DETECT --> ROUTE{文件类型路由}

    ROUTE -->|PDF文档| PDF_PARSER[PDF解析器<br/>pdfminer/PyPDF2<br/>文本+元数据提取]
    ROUTE -->|Office文档| OFFICE_PARSER[Office解析器<br/>python-docx/openpyxl<br/>内容结构化提取]
    ROUTE -->|文本文件| TEXT_PARSER[文本解析器<br/>编码检测+内容读取<br/>MD/TXT/代码文件]
    ROUTE -->|图像文件| IMAGE_PARSER[图像解析器<br/>OCR文字识别<br/>EXIF元数据提取]
    ROUTE -->|其他文件| META_PARSER[元数据解析器<br/>基础文件属性<br/>名称/大小/时间]

    PDF_PARSER --> CONTENT_CLEAN[内容清洗<br/>去除噪声<br/>格式标准化]
    OFFICE_PARSER --> CONTENT_CLEAN
    TEXT_PARSER --> CONTENT_CLEAN
    IMAGE_PARSER --> CONTENT_CLEAN
    META_PARSER --> CONTENT_CLEAN

    CONTENT_CLEAN --> CHUNK[文本分块<br/>语义边界切分<br/>重叠窗口处理]

    CHUNK --> EXTRACT_COMPLETE[内容提取完成<br/>结构化数据输出]

    style DETECT fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style CONTENT_CLEAN fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style CHUNK fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

**核心技术实现**：

| 文件类型 | 解析技术 | 提取内容 | 处理难点 |
|----------|----------|----------|----------|
| **PDF文档** | pdfminer + PyPDF2 | 文本+表格+图片 | 复杂布局识别 |
| **Word文档** | python-docx | 正文+样式+表格 | 嵌入对象处理 |
| **Excel表格** | openpyxl + pandas | 数据+公式+图表 | 大文件内存优化 |
| **PowerPoint** | python-pptx | 文本+备注+媒体 | 多媒体内容提取 |
| **文本文件** | chardet + 直接读取 | 纯文本内容 | 编码检测 |
| **图像文件** | Tesseract OCR | OCR文字+EXIF | 图像预处理 |

#### 2.3.3 索引子系统

##### 2.3.3.1 元数据索引

```mermaid
graph TB
    subgraph "元数据索引架构"
        FILE_SCAN[文件系统扫描<br/>递归遍历<br/>权限检查] --> META_EXTRACT[元数据提取<br/>文件属性<br/>系统信息]

        META_EXTRACT --> SQLITE_STORE[SQLite存储<br/>关系型数据库<br/>ACID事务保证]

        SQLITE_STORE --> INDEX_OPT[索引优化<br/>B-Tree索引<br/>复合索引]

        INDEX_OPT --> CACHE_LAYER[缓存层<br/>LRU内存缓存<br/>热点数据预加载]

        CACHE_LAYER --> QUERY_ENGINE[查询引擎<br/>SQL优化<br/>并发控制]
    end

    subgraph "实时更新机制"
        FS_MONITOR[文件系统监控<br/>inotify/FSEvents<br/>NTFS变更日志] --> CHANGE_DETECT[变更检测<br/>增删改事件<br/>批量处理]

        CHANGE_DETECT --> INCREMENTAL[增量更新<br/>差异计算<br/>最小化IO]

        INCREMENTAL --> SQLITE_STORE
    end

    style SQLITE_STORE fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style CACHE_LAYER fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style FS_MONITOR fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

**数据库设计**：

```sql
-- 文件元数据表
CREATE TABLE file_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT UNIQUE NOT NULL,
    file_name TEXT NOT NULL,
    file_size INTEGER,
    file_type TEXT,
    mime_type TEXT,
    created_time TIMESTAMP,
    modified_time TIMESTAMP,
    accessed_time TIMESTAMP,
    permissions TEXT,
    checksum TEXT,
    indexed_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 性能优化索引
CREATE INDEX idx_file_name ON file_metadata(file_name);
CREATE INDEX idx_file_type ON file_metadata(file_type);
CREATE INDEX idx_modified_time ON file_metadata(modified_time);
CREATE INDEX idx_file_size ON file_metadata(file_size);
CREATE UNIQUE INDEX idx_file_path ON file_metadata(file_path);
```

##### 2.3.3.2 全文索引

```mermaid
graph TB
    subgraph "全文索引构建"
        TEXT_INPUT[文本内容输入<br/>文档解析结果<br/>清洗后文本] --> TOKENIZE[分词处理<br/>中文分词<br/>停用词过滤]

        TOKENIZE --> WHOOSH_INDEX[Whoosh索引<br/>倒排索引结构<br/>TF-IDF权重]

        WHOOSH_INDEX --> BM25_SCORE[BM25评分<br/>相关性计算<br/>文档排序]
    end

    subgraph "搜索优化"
        QUERY_PARSE[查询解析<br/>语法分析<br/>查询扩展] --> SEARCH_ENGINE[搜索引擎<br/>并行搜索<br/>结果合并]

        SEARCH_ENGINE --> HIGHLIGHT[结果高亮<br/>关键词标记<br/>上下文提取]

        HIGHLIGHT --> CACHE_RESULT[结果缓存<br/>查询缓存<br/>性能优化]
    end

    WHOOSH_INDEX --> SEARCH_ENGINE
    BM25_SCORE --> SEARCH_ENGINE

    style WHOOSH_INDEX fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style BM25_SCORE fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style SEARCH_ENGINE fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

**技术实现特点**：

| 组件 | 技术选型 | 核心功能 | 性能指标 |
|------|----------|----------|----------|
| **分词器** | jieba + 自定义词典 | 中文智能分词 | >10MB/s处理速度 |
| **索引引擎** | Whoosh + 自定义优化 | 倒排索引构建 | <100ms查询响应 |
| **评分算法** | BM25 + 自定义权重 | 相关性排序 | 准确率>90% |
| **缓存系统** | LRU + 预测缓存 | 查询加速 | 缓存命中率>80% |

##### 2.3.3.3 语义索引

```mermaid
graph TB
    subgraph "语义向量化"
        TEXT_CHUNK[文本分块<br/>语义边界切分<br/>重叠窗口] --> EMBEDDING[文本嵌入<br/>BGE中文模型<br/>768维向量]

        EMBEDDING --> VECTOR_NORM[向量标准化<br/>L2归一化<br/>维度优化]

        VECTOR_NORM --> FAISS_INDEX[FAISS索引<br/>向量数据库<br/>相似度搜索]
    end

    subgraph "语义搜索"
        QUERY_EMBED[查询向量化<br/>同模型编码<br/>查询理解] --> SIMILARITY[相似度计算<br/>余弦相似度<br/>欧氏距离]

        SIMILARITY --> VECTOR_SEARCH[向量检索<br/>近似最近邻<br/>Top-K结果]

        VECTOR_SEARCH --> RERANK[语义重排<br/>上下文匹配<br/>相关性优化]
    end

    FAISS_INDEX --> VECTOR_SEARCH

    subgraph "模型管理"
        MODEL_LOAD[模型加载<br/>BGE-large-zh<br/>本地部署] --> MODEL_CACHE[模型缓存<br/>内存驻留<br/>推理加速]

        MODEL_CACHE --> BATCH_PROCESS[批量处理<br/>向量化优化<br/>GPU加速]
    end

    MODEL_LOAD --> EMBEDDING
    MODEL_CACHE --> QUERY_EMBED

    style FAISS_INDEX fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style EMBEDDING fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style SIMILARITY fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

**语义搜索技术栈**：

| 层级 | 技术组件 | 功能描述 | 性能要求 |
|------|----------|----------|----------|
| **模型层** | BGE-large-zh-v1.5 | 中文语义理解 | <200ms单次推理 |
| **向量层** | FAISS + 量化索引 | 高效相似度搜索 | <50ms检索响应 |
| **缓存层** | Redis + 内存缓存 | 向量结果缓存 | >95%缓存命中率 |
| **优化层** | 批量处理 + GPU | 并行向量化 | >100文档/分钟 |

#### 2.3.4 检索、融合与排序

```mermaid
graph TB
    subgraph "多引擎并行检索"
        QUERY_INPUT[用户查询输入] --> INTENT_ANALYSIS[意图分析<br/>查询类型识别<br/>策略选择]

        INTENT_ANALYSIS --> PARALLEL_SEARCH[并行搜索调度]

        PARALLEL_SEARCH --> META_SEARCH[元数据检索<br/>文件名/路径<br/>属性过滤]
        PARALLEL_SEARCH --> FULL_SEARCH[全文检索<br/>关键词匹配<br/>BM25评分]
        PARALLEL_SEARCH --> SEM_SEARCH[语义检索<br/>向量相似度<br/>语义理解]
    end

    subgraph "结果融合算法"
        META_SEARCH --> RESULT_COLLECT[结果收集<br/>多源数据汇总<br/>去重处理]
        FULL_SEARCH --> RESULT_COLLECT
        SEM_SEARCH --> RESULT_COLLECT

        RESULT_COLLECT --> SCORE_FUSION[分数融合<br/>RRF算法<br/>加权平均]

        SCORE_FUSION --> RERANK[智能重排<br/>多因子排序<br/>个性化调整]

        RERANK --> FINAL_RESULT[最终结果<br/>统一格式<br/>相关性排序]
    end

    subgraph "排序优化"
        USER_CONTEXT[用户上下文<br/>搜索历史<br/>偏好学习] --> PERSONALIZE[个性化排序<br/>用户画像<br/>行为分析]

        PERSONALIZE --> DIVERSITY[结果多样性<br/>类型平衡<br/>避免重复]

        DIVERSITY --> RERANK
    end

    style INTENT_ANALYSIS fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style SCORE_FUSION fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style RERANK fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

**融合算法核心**：

```python
def reciprocal_rank_fusion(results_list, k=60):
    """
    RRF (Reciprocal Rank Fusion) 算法实现
    融合多个搜索引擎的结果
    """
    fused_scores = {}

    for results in results_list:
        for rank, doc in enumerate(results, 1):
            doc_id = doc['id']
            score = 1.0 / (k + rank)

            if doc_id in fused_scores:
                fused_scores[doc_id] += score
            else:
                fused_scores[doc_id] = score

    # 按融合分数排序
    return sorted(fused_scores.items(),
                 key=lambda x: x[1], reverse=True)
```

**排序因子权重**：

| 排序因子 | 权重 | 说明 | 调整策略 |
|----------|------|------|----------|
| **相关性分数** | 40% | 内容匹配度 | 基于查询类型动态调整 |
| **文件类型偏好** | 20% | 用户偏好 | 学习用户行为模式 |
| **时间新鲜度** | 15% | 修改时间 | 可配置衰减函数 |
| **访问频率** | 15% | 历史访问 | 基于使用统计 |
| **文件大小** | 10% | 内容丰富度 | 类型相关权重 |



## 3. 项目目录结构

```mermaid
graph TB
    subgraph "用户交互层（第一阶段）"
        STREAMLIT[🖥️ Streamlit Web界面<br/>Python + 即时搜索<br/>简洁易用的UI]
    end

    subgraph "智能路由层（核心）"
        ROUTER[🧠 查询路由器<br/>意图判断 + 策略选择]
        INTENT[🎯 意图识别<br/>文件名模式/内容关键词/自然语言]
    end

    subgraph "双引擎检索（第一阶段）"
        METADATA[⚡ 元数据检索<br/>支持所有文件类型<br/>文件名/路径/时间/大小<br/>包括音频/视频/图像]

        FULLTEXT[📚 全文检索<br/>仅支持文本文档<br/>PDF/Office/MD/TXT<br/>BM25算法]
    end

    subgraph "文档处理层"
        META_PARSER[📁 元数据解析器<br/>所有文件类型的基础信息<br/>文件名/大小/时间/类型]

        TEXT_PARSER[📄 文本解析器<br/>PDF: pdfminer<br/>Office: python-docx/openpyxl<br/>MD/TXT: 直接读取]

        FS_MONITOR[👁️ 文件系统监听<br/>实时索引更新<br/>增量处理]
    end

    subgraph "存储层"
        META_INDEX[📊 元数据索引<br/>SQLite + 内存缓存<br/>支持复杂查询]

        FULL_INDEX[📚 全文索引<br/>Whoosh倒排索引<br/>关键词匹配]
    end

    subgraph "结果展示层"
        FUSION[🔄 结果融合<br/>多源结果合并]
        DISPLAY[📋 文件展示<br/>Streamlit组件展示]
    end

    STREAMLIT --> INTENT
    INTENT --> ROUTER

    ROUTER --> METADATA
    ROUTER --> FULLTEXT

    METADATA --> META_PARSER
    FULLTEXT --> TEXT_PARSER

    META_PARSER --> META_INDEX
    TEXT_PARSER --> FULL_INDEX

    FS_MONITOR --> META_INDEX
    FS_MONITOR --> FULL_INDEX

    METADATA --> FUSION
    FULLTEXT --> FUSION

    FUSION --> DISPLAY
    DISPLAY --> STREAMLIT

    style ROUTER fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style METADATA fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style FULLTEXT fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style STREAMLIT fill:#ff9800,stroke:#f57c00,stroke-width:3px
```

```
local-ai-search/
├── backend/                  # FastAPI后端服务
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py          # FastAPI主应用
│   │   ├── config.py        # 配置管理
│   │   ├── api/             # API路由
│   │   │   ├── __init__.py
│   │   │   ├── search.py    # 搜索API
│   │   │   ├── admin.py     # 管理API
│   │   │   ├── semantic.py  # 语义搜索API
│   │   │   └── health.py    # 健康检查API
│   │   ├── core/            # 核心业务逻辑
│   │   │   ├── __init__.py
│   │   │   ├── intent_analyzer.py  # 意图分析器
│   │   │   ├── query_router.py     # 查询路由器
│   │   │   ├── result_fusion.py    # 结果融合器
│   │   │   └── ranking.py          # 智能排序算法
│   │   ├── engines/         # 检索引擎
│   │   │   ├── __init__.py
│   │   │   ├── metadata_engine.py  # 元数据检索引擎
│   │   │   ├── fulltext_engine.py  # 全文检索引擎
│   │   │   ├── semantic_engine.py  # 语义检索引擎
│   │   │   └── hybrid_engine.py    # 混合检索引擎
│   │   ├── models/          # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── file_info.py        # 文件信息模型
│   │   │   ├── search_result.py    # 搜索结果模型
│   │   │   ├── api_models.py       # API请求响应模型
│   │   │   └── semantic_models.py  # 语义搜索模型
│   │   ├── parsers/         # 文档解析器
│   │   │   ├── __init__.py
│   │   │   ├── base_parser.py      # 基础解析器
│   │   │   ├── pdf_parser.py       # PDF解析器
│   │   │   ├── office_parser.py    # Office文档解析器
│   │   │   ├── text_parser.py      # 文本文件解析器
│   │   │   └── content_extractor.py # 内容提取器
│   │   ├── semantic/        # 语义搜索模块
│   │   │   ├── __init__.py
│   │   │   ├── embeddings.py       # 文本嵌入
│   │   │   ├── similarity.py       # 相似度计算
│   │   │   ├── model_manager.py    # 模型管理
│   │   │   └── vector_index.py     # 向量索引
│   │   ├── storage/         # 存储层
│   │   │   ├── __init__.py
│   │   │   ├── metadata_store.py   # 元数据存储
│   │   │   ├── fulltext_store.py   # 全文索引存储
│   │   │   ├── vector_store.py     # 向量索引存储
│   │   │   └── cache_manager.py    # 缓存管理
│   │   └── utils/           # 工具函数
│   │       ├── __init__.py
│   │       ├── file_monitor.py     # 文件系统监控
│   │       ├── text_processor.py   # 文本处理
│   │       ├── performance.py      # 性能监控
│   │       └── logger.py           # 日志工具
│   └── requirements.txt     # 后端依赖
├── frontend/                # Streamlit前端界面
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py          # Streamlit主应用
│   │   ├── config.py        # 前端配置
│   │   ├── pages/           # 页面组件
│   │   │   ├── __init__.py
│   │   │   ├── search_page.py      # 搜索页面
│   │   │   ├── semantic_page.py    # 语义搜索页面
│   │   │   ├── admin_page.py       # 管理页面
│   │   │   └── stats_page.py       # 统计页面
│   │   ├── components/      # UI组件
│   │   │   ├── __init__.py
│   │   │   ├── search_box.py       # 搜索框组件
│   │   │   ├── result_list.py      # 结果列表组件
│   │   │   ├── semantic_ui.py      # 语义搜索UI
│   │   │   └── sidebar.py          # 侧边栏组件
│   │   ├── services/        # API调用服务
│   │   │   ├── __init__.py
│   │   │   ├── search_api.py       # 搜索API调用
│   │   │   ├── semantic_api.py     # 语义搜索API调用
│   │   │   └── admin_api.py        # 管理API调用
│   │   └── utils/           # 前端工具
│   │       ├── __init__.py
│   │       ├── formatters.py       # 格式化工具
│   │       ├── validators.py       # 验证工具
│   │       └── ui_helpers.py       # UI辅助函数
│   ├── .streamlit/          # Streamlit配置
│   │   └── config.toml      # Streamlit配置文件
│   └── requirements.txt     # 前端依赖
├── models/                  # AI模型文件
│   ├── embeddings/          # 嵌入模型
│   │   ├── bge-large-zh/    # BGE中文模型
│   │   └── sentence-transformers/ # 其他嵌入模型
│   └── cache/               # 模型缓存
├── data/                    # 数据目录
│   ├── indexes/             # 索引文件
│   │   ├── metadata/        # 元数据索引
│   │   ├── fulltext/        # 全文索引
│   │   └── vectors/         # 向量索引
│   ├── cache/               # 缓存文件
│   └── logs/                # 日志文件
├── tests/                   # 测试文件
│   ├── backend/             # 后端测试
│   │   ├── unit/            # 单元测试
│   │   ├── integration/     # 集成测试
│   │   ├── api/             # API测试
│   │   └── semantic/        # 语义搜索测试
│   └── frontend/            # 前端测试
│       └── ui/              # UI测试
├── docs/                    # 文档
│   ├── api.md               # API文档
│   ├── semantic.md          # 语义搜索文档
│   ├── deployment.md        # 部署文档
│   └── user_guide.md        # 用户指南
├── scripts/                 # 脚本文件
│   ├── start_backend.sh     # 启动后端脚本
│   ├── start_frontend.sh    # 启动前端脚本
│   ├── setup_env.sh         # 环境配置脚本
│   └── download_models.sh   # 模型下载脚本
├── environment.yml          # Conda环境配置
├── README.md                # 项目说明
└── .gitignore              # Git忽略文件
```

### 目录设计说明

#### 🎯 **核心设计原则**

1. **前后端分离**：backend和frontend独立开发部署
2. **模块化设计**：按功能划分，便于维护和扩展
3. **语义搜索支持**：完整的semantic模块和models目录
4. **分层架构**：API层、业务层、引擎层、存储层清晰分离

#### 📁 **关键目录说明**

| 目录 | 功能 | 重要文件 |
|------|------|----------|
| **backend/app/semantic/** | 语义搜索核心模块 | embeddings.py, similarity.py |
| **models/embeddings/** | AI模型存储 | bge-large-zh/ |
| **data/indexes/vectors/** | 向量索引存储 | FAISS索引文件 |
| **frontend/app/pages/semantic_page.py** | 语义搜索界面 | 语义搜索专用UI |
| **tests/backend/semantic/** | 语义搜索测试 | 模型和算法测试 |

---

## 4. 分阶段架构设计

### 4.1 第一阶段架构（MVP - 5-6周）

**目标**：实现元数据检索 + 全文检索 + 基础智能路由

```mermaid
graph TB
    subgraph "第一阶段：MVP核心功能"
        subgraph "前端层"
            ST1[🖥️ Streamlit界面<br/>搜索框 + 结果展示<br/>基础管理功能]
        end

        subgraph "API层"
            FA1[🚀 FastAPI服务<br/>搜索API + 管理API<br/>基础路由逻辑]
        end

        subgraph "双引擎检索"
            META1[⚡ 元数据引擎<br/>SQLite + 文件属性<br/>支持所有文件类型]
            FULL1[📚 全文引擎<br/>Whoosh + BM25<br/>PDF/Office/TXT/MD]
        end

        subgraph "数据层"
            SQLITE1[🗄️ SQLite数据库<br/>文件元数据存储]
            WHOOSH1[📊 Whoosh索引<br/>全文检索索引]
        end
    end

    ST1 --> FA1
    FA1 --> META1
    FA1 --> FULL1
    META1 --> SQLITE1
    FULL1 --> WHOOSH1

    style FA1 fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style META1 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style FULL1 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

#### 核心功能范围

| 功能模块 | 技术实现 | 支持范围 | 性能目标 |
|----------|----------|----------|----------|
| **元数据检索** | SQLite + 文件系统API | 所有文件类型 | <10ms响应 |
| **全文检索** | Whoosh + BM25算法 | PDF/Office/TXT/MD | <100ms响应 |
| **文档解析** | pdfminer + python-docx | 常用文档格式 | 100文档/分钟 |
| **API服务** | FastAPI + Pydantic | RESTful接口 | 1000 QPS |
| **前端界面** | Streamlit | Web界面 | <2s加载时间 |

#### 技术实现重点

**FastAPI后端架构**：
- 异步处理提升并发性能
- 自动API文档生成
- 请求验证和错误处理
- 健康检查和监控接口

**Streamlit前端特点**：
- Python原生开发，快速迭代
- 实时搜索和结果展示
- 响应式布局适配
- 组件化设计便于维护

**数据存储策略**：
- SQLite轻量级部署
- 文件系统直接访问
- 增量索引更新
- 缓存优化查询性能

### 4.2 第二阶段架构（智能化 - 4-5周）

**目标**：增加语义检索 + 智能路由优化 + 结果融合

```mermaid
graph TB
    subgraph "第二阶段：智能化升级"
        subgraph "前端层"
            ST2[🖥️ Streamlit界面<br/>智能搜索建议<br/>高级过滤选项<br/>搜索历史]
        end

        subgraph "API层"
            FA2[🚀 FastAPI服务<br/>智能路由API<br/>意图分析API<br/>结果融合API]
        end

        subgraph "智能路由层"
            INTENT2[🎯 意图识别<br/>规则 + ML分类<br/>查询理解]
            ROUTER2[🧠 智能路由器<br/>策略选择<br/>并行调度]
            FUSION2[🔄 结果融合<br/>RRF算法<br/>智能排序]
        end

        subgraph "三引擎检索"
            META2[⚡ 元数据引擎<br/>增强过滤<br/>时间/类型/大小]
            FULL2[📚 全文引擎<br/>优化BM25<br/>高亮显示]
            SEM2[🧠 语义引擎<br/>BGE中文模型<br/>向量相似度]
        end

        subgraph "数据层"
            SQLITE2[🗄️ SQLite数据库<br/>优化索引结构]
            WHOOSH2[📊 Whoosh索引<br/>增量更新优化]
            VECTOR2[🔢 向量存储<br/>FAISS本地索引]
        end
    end

    ST2 --> FA2
    FA2 --> INTENT2
    INTENT2 --> ROUTER2
    ROUTER2 --> META2
    ROUTER2 --> FULL2
    ROUTER2 --> SEM2
    META2 --> SQLITE2
    FULL2 --> WHOOSH2
    SEM2 --> VECTOR2
    META2 --> FUSION2
    FULL2 --> FUSION2
    SEM2 --> FUSION2
    FUSION2 --> FA2

    style FA2 fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style INTENT2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style SEM2 fill:#fce4ec,stroke:#c2185b,stroke-width:2px
```

#### 新增功能模块

| 功能模块 | 技术实现 | 创新点 | 性能提升 |
|----------|----------|--------|----------|
| **语义检索** | BGE中文模型 + FAISS | 本地化语义理解 | 准确率提升30% |
| **智能路由** | 规则引擎 + ML分类 | 自动策略选择 | 响应时间优化20% |
| **结果融合** | RRF算法 + 重排序 | 多源结果整合 | 相关性提升25% |
| **意图识别** | 模式匹配 + NLP | 查询理解 | 用户体验提升40% |

#### 技术难点攻克

**语义搜索本地化**：
- BGE中文模型集成
- 向量索引构建优化
- 相似度计算加速
- 内存使用控制

**智能路由算法**：
- 查询特征提取
- 意图分类模型
- 策略选择逻辑
- 性能监控反馈

### 4.3 第三阶段架构（优化完善 - 3-4周）

**目标**：性能优化 + 用户体验完善 + 高级功能

```mermaid
graph TB
    subgraph "第三阶段：优化完善"
        subgraph "前端层"
            ST3[🖥️ Streamlit界面<br/>个性化推荐<br/>搜索分析<br/>批量操作<br/>导出功能]
        end

        subgraph "API层"
            FA3[🚀 FastAPI服务<br/>性能监控API<br/>用户偏好API<br/>批量处理API<br/>统计分析API]
        end

        subgraph "智能增强层"
            INTENT3[🎯 意图识别<br/>深度学习模型<br/>上下文理解]
            ROUTER3[🧠 智能路由器<br/>自适应策略<br/>负载均衡]
            FUSION3[🔄 结果融合<br/>个性化排序<br/>多样性优化]
            RECOMMEND[💡 智能推荐<br/>相关文件推荐<br/>搜索建议]
        end

        subgraph "优化检索层"
            META3[⚡ 元数据引擎<br/>缓存优化<br/>并发处理]
            FULL3[📚 全文引擎<br/>分片索引<br/>增量更新]
            SEM3[🧠 语义引擎<br/>模型量化<br/>批量推理]
        end

        subgraph "存储优化层"
            CACHE3[💾 多级缓存<br/>LRU + 预测缓存]
            SQLITE3[🗄️ SQLite数据库<br/>分区表 + 索引优化]
            WHOOSH3[📊 Whoosh索引<br/>压缩存储]
            VECTOR3[🔢 向量存储<br/>量化索引 + 分片]
        end
    end

    ST3 --> FA3
    FA3 --> INTENT3
    INTENT3 --> ROUTER3
    ROUTER3 --> META3
    ROUTER3 --> FULL3
    ROUTER3 --> SEM3
    META3 --> CACHE3
    FULL3 --> CACHE3
    SEM3 --> CACHE3
    CACHE3 --> SQLITE3
    CACHE3 --> WHOOSH3
    CACHE3 --> VECTOR3
    META3 --> FUSION3
    FULL3 --> FUSION3
    SEM3 --> FUSION3
    FUSION3 --> RECOMMEND
    RECOMMEND --> FA3

    style FA3 fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style RECOMMEND fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style CACHE3 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

---

#### 性能优化重点

| 优化方向 | 技术手段 | 预期效果 | 实现难度 |
|----------|----------|----------|----------|
| **查询性能** | 多级缓存 + 预测加载 | 响应时间减少50% | 中等 |
| **索引效率** | 分片存储 + 并行处理 | 索引速度提升3倍 | 较高 |
| **内存优化** | 模型量化 + 懒加载 | 内存占用减少40% | 中等 |
| **并发处理** | 异步队列 + 连接池 | 并发能力提升5倍 | 较高 |

#### 用户体验增强

**个性化功能**：
- 搜索历史分析
- 个人偏好学习
- 智能推荐算法
- 自定义界面配置

**高级功能**：
- 批量文件操作
- 搜索结果导出
- 统计分析报告
- 系统性能监控

---

---

## 5. 分阶段开发计划

```mermaid
gantt
    title 第一阶段开发计划（5-6周）
    dateFormat  YYYY-MM-DD

    section 基础架构
    项目结构搭建         :done, setup, 2024-01-01, 3d
    数据库设计          :done, db, after setup, 2d
    配置管理           :config, after db, 2d

    section 元数据索引（第1-2周）
    元数据解析器         :meta1, 2024-01-08, 5d
    SQLite索引存储      :meta2, after meta1, 3d
    文件系统监控         :meta3, after meta2, 4d
    元数据搜索功能       :meta4, after meta3, 3d

    section 文档解析（第2-3周）
    PDF解析器           :pdf, 2024-01-15, 4d
    Office解析器        :office, after pdf, 4d
    文本解析器          :text, after office, 3d
    解析器集成测试       :parse_test, after text, 2d

    section 全文检索（第3-4周）
    Whoosh索引设计      :whoosh1, 2024-01-22, 3d
    BM25搜索实现        :whoosh2, after whoosh1, 4d
    增量索引更新         :whoosh3, after whoosh2, 3d
    全文搜索功能         :whoosh4, after whoosh3, 3d

    section 意图分析（第4周）
    意图分析器开发       :intent1, 2024-01-29, 4d
    查询路由器实现       :intent2, after intent1, 3d
    路由策略优化         :intent3, after intent2, 3d

    section Streamlit界面（第5周）
    Streamlit项目搭建    :st1, 2024-02-05, 2d
    搜索页面开发         :st2, after st1, 3d
    结果展示组件         :st3, after st2, 3d
    管理页面开发         :st4, after st3, 2d
    UI样式优化          :st5, after st4, 2d

    section 测试优化（第6周）
    单元测试编写         :test1, 2024-02-12, 3d
    集成测试            :test2, after test1, 3d
    性能优化            :perf, after test2, 3d
    文档编写            :docs, after perf, 2d
    部署准备            :deploy, after docs, 2d
```

### 5.1 第一阶段开发计划

#### 5.1.1 环境搭建与基础架构（第1周）

**目标**：完成开发环境配置和项目基础架构搭建

```mermaid
gantt
    title 第一周：环境搭建与基础架构
    dateFormat  YYYY-MM-DD

    section 环境配置
    Python环境搭建        :env1, 2024-01-01, 2d
    依赖包安装           :env2, after env1, 1d
    开发工具配置         :env3, after env2, 1d

    section 项目结构
    目录结构创建         :proj1, 2024-01-01, 1d
    配置文件设计         :proj2, after proj1, 2d
    日志系统搭建         :proj3, after proj2, 2d

    section 数据库设计
    SQLite数据库设计     :db1, 2024-01-04, 2d
    表结构创建          :db2, after db1, 1d
```

**关键任务**：
- [x] 创建Conda虚拟环境
- [x] 安装核心依赖包（FastAPI、Streamlit、SQLite等）
- [x] 设计项目目录结构
- [x] 配置开发环境（IDE、调试工具）
- [x] 建立版本控制系统

#### 5.1.2 元数据索引开发（第2-3周）

**目标**：实现高性能文件元数据索引系统

```python
# 核心开发任务示例
class MetadataIndexer:
    """元数据索引器 - 第一阶段核心组件"""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection = sqlite3.connect(db_path)
        self.setup_database()

    def scan_directory(self, root_path: str) -> int:
        """扫描目录并建立索引"""
        file_count = 0
        for root, dirs, files in os.walk(root_path):
            for file in files:
                file_path = os.path.join(root, file)
                self.index_file(file_path)
                file_count += 1
        return file_count

    def index_file(self, file_path: str):
        """索引单个文件"""
        try:
            stat = os.stat(file_path)
            file_info = {
                'path': file_path,
                'name': os.path.basename(file_path),
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'type': self.get_file_type(file_path)
            }
            self.insert_file_record(file_info)
        except Exception as e:
            logger.error(f"索引文件失败: {file_path}, 错误: {e}")
```

**开发里程碑**：
- 文件系统扫描器完成
- SQLite数据库操作封装
- 基础搜索功能实现
- 性能测试通过（>10,000文件/分钟）

#### 5.1.3 文档解析器开发（第3-4周）

**目标**：实现多格式文档内容提取

```python
class DocumentParser:
    """文档解析器工厂"""

    def __init__(self):
        self.parsers = {
            '.pdf': PDFParser(),
            '.docx': WordParser(),
            '.xlsx': ExcelParser(),
            '.txt': TextParser(),
            '.md': MarkdownParser()
        }

    def parse(self, file_path: str) -> Dict[str, Any]:
        """解析文档内容"""
        ext = os.path.splitext(file_path)[1].lower()
        parser = self.parsers.get(ext)

        if parser:
            return parser.extract_content(file_path)
        else:
            return self.extract_metadata_only(file_path)
```

#### 5.1.4 全文索引实现（第4-5周）

**目标**：基于Whoosh的全文检索系统

```python
from whoosh import index
from whoosh.fields import Schema, TEXT, ID, DATETIME
from whoosh.qparser import QueryParser

class FullTextIndexer:
    """全文索引器"""

    def __init__(self, index_dir: str):
        self.index_dir = index_dir
        self.schema = Schema(
            path=ID(stored=True),
            title=TEXT(stored=True),
            content=TEXT(analyzer=ChineseAnalyzer()),
            modified=DATETIME(stored=True)
        )
        self.ix = self.create_or_open_index()

    def add_document(self, doc_info: Dict):
        """添加文档到索引"""
        writer = self.ix.writer()
        writer.add_document(**doc_info)
        writer.commit()

    def search(self, query_str: str, limit: int = 50):
        """全文搜索"""
        with self.ix.searcher() as searcher:
            query = QueryParser("content", self.ix.schema).parse(query_str)
            results = searcher.search(query, limit=limit)
            return [dict(hit) for hit in results]
```

#### 5.1.5 FastAPI后端开发（第5周）

**目标**：构建RESTful API服务

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

app = FastAPI(title="本地AI检索系统API")

class SearchRequest(BaseModel):
    query: str
    search_type: str = "auto"  # auto, metadata, fulltext
    limit: int = 50

@app.post("/api/search")
async def search_files(request: SearchRequest):
    """统一搜索接口"""
    try:
        # 路由到相应的搜索引擎
        if request.search_type == "metadata":
            results = metadata_engine.search(request.query)
        elif request.search_type == "fulltext":
            results = fulltext_engine.search(request.query)
        else:
            # 智能路由逻辑
            results = smart_router.route_search(request.query)

        return {
            "query": request.query,
            "total": len(results),
            "results": results[:request.limit]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

#### 5.1.6 Streamlit前端开发（第6周）

**目标**：用户友好的Web界面

```python
import streamlit as st
import requests

def main():
    st.title("🔍 本地AI文件搜索")

    # 搜索框
    query = st.text_input("搜索文件", placeholder="输入文件名或内容关键词...")

    if query:
        # 调用后端API
        response = requests.post(
            "http://localhost:8000/api/search",
            json={"query": query, "limit": 50}
        )

        if response.status_code == 200:
            results = response.json()
            display_results(results)
        else:
            st.error("搜索失败")

def display_results(results):
    """显示搜索结果"""
    st.write(f"找到 {results['total']} 个结果")

    for result in results['results']:
        with st.container():
            col1, col2 = st.columns([3, 1])

            with col1:
                st.write(f"**{result['name']}**")
                st.caption(result['path'])

            with col2:
                if st.button("打开", key=result['path']):
                    open_file(result['path'])

if __name__ == "__main__":
    main()
```

### 5.2 第二阶段开发计划

#### 5.2.1 语义搜索引擎（第7-9周）

**目标**：集成BGE模型实现语义搜索

```python
from sentence_transformers import SentenceTransformer
import faiss
import numpy as np

class SemanticSearchEngine:
    """语义搜索引擎"""

    def __init__(self, model_name: str = "BAAI/bge-large-zh-v1.5"):
        self.model = SentenceTransformer(model_name)
        self.index = None
        self.doc_store = {}

    def build_index(self, documents: List[Dict]):
        """构建语义索引"""
        texts = [doc['content'] for doc in documents]
        embeddings = self.model.encode(texts)

        # 构建FAISS索引
        dimension = embeddings.shape[1]
        self.index = faiss.IndexFlatIP(dimension)
        self.index.add(embeddings.astype('float32'))

        # 存储文档映射
        for i, doc in enumerate(documents):
            self.doc_store[i] = doc

    def semantic_search(self, query: str, top_k: int = 10):
        """语义搜索"""
        query_embedding = self.model.encode([query])
        scores, indices = self.index.search(
            query_embedding.astype('float32'), top_k
        )

        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx in self.doc_store:
                result = self.doc_store[idx].copy()
                result['semantic_score'] = float(score)
                results.append(result)

        return results
```

#### 5.2.2 智能路由系统（第9-10周）

**目标**：实现查询意图识别和智能路由

```python
import re
from typing import List, Dict, Tuple

class QueryRouter:
    """查询路由器 - 智能分发搜索请求"""

    def __init__(self):
        self.filename_patterns = [
            r'\.(pdf|doc|docx|txt|md|xlsx|ppt|pptx)$',
            r'^\w+\.\w+$',  # 简单文件名模式
            r'^\*\.\w+$'    # 通配符模式
        ]

        self.content_keywords = [
            '包含', '内容', '正文', '文档中', '提到', '关于'
        ]

        self.semantic_indicators = [
            '类似', '相关', '相似', '像这样', '这种', '找一些'
        ]

    def analyze_intent(self, query: str) -> Dict[str, float]:
        """分析查询意图"""
        scores = {
            'filename': 0.0,
            'content': 0.0,
            'semantic': 0.0
        }

        # 文件名模式检测
        for pattern in self.filename_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                scores['filename'] += 0.3

        # 内容关键词检测
        for keyword in self.content_keywords:
            if keyword in query:
                scores['content'] += 0.2

        # 语义指示词检测
        for indicator in self.semantic_indicators:
            if indicator in query:
                scores['semantic'] += 0.3

        # 查询长度启发式
        if len(query) > 20:
            scores['semantic'] += 0.2
        elif len(query) < 10:
            scores['filename'] += 0.2

        return scores

    def route_query(self, query: str) -> str:
        """路由查询到最佳搜索引擎"""
        intent_scores = self.analyze_intent(query)
        best_intent = max(intent_scores, key=intent_scores.get)

        # 如果分数太接近，使用混合搜索
        max_score = intent_scores[best_intent]
        if max_score < 0.4:
            return 'hybrid'

        return best_intent
```

#### 5.2.3 结果融合算法（第10-11周）

**目标**：多源搜索结果智能融合

```python
class ResultFusion:
    """搜索结果融合器"""

    def __init__(self):
        self.weights = {
            'filename': 0.4,
            'content': 0.3,
            'semantic': 0.3
        }

    def fuse_results(self, results_dict: Dict[str, List]) -> List[Dict]:
        """融合多个搜索引擎的结果"""
        # 收集所有唯一文档
        all_docs = {}

        for engine, results in results_dict.items():
            weight = self.weights.get(engine, 0.2)

            for i, doc in enumerate(results):
                doc_id = doc['path']
                rank_score = 1.0 / (i + 1)  # 排名分数
                weighted_score = rank_score * weight

                if doc_id in all_docs:
                    all_docs[doc_id]['fused_score'] += weighted_score
                    all_docs[doc_id]['sources'].append(engine)
                else:
                    doc['fused_score'] = weighted_score
                    doc['sources'] = [engine]
                    all_docs[doc_id] = doc

        # 按融合分数排序
        fused_results = list(all_docs.values())
        fused_results.sort(key=lambda x: x['fused_score'], reverse=True)

        return fused_results
```

### 5.3 第三阶段开发计划

#### 5.3.1 性能优化（第12-13周）

**目标**：系统性能调优和资源优化

**优化重点**：
- 索引构建速度优化
- 搜索响应时间优化
- 内存使用优化
- 并发处理能力提升

```python
class PerformanceOptimizer:
    """性能优化器"""

    def __init__(self):
        self.cache = {}
        self.query_stats = {}

    def optimize_search(self, query: str, search_func):
        """搜索性能优化"""
        # 查询缓存
        cache_key = hash(query)
        if cache_key in self.cache:
            return self.cache[cache_key]

        # 执行搜索
        start_time = time.time()
        results = search_func(query)
        end_time = time.time()

        # 缓存结果
        self.cache[cache_key] = results

        # 记录性能统计
        self.query_stats[query] = {
            'response_time': end_time - start_time,
            'result_count': len(results),
            'timestamp': datetime.now()
        }

        return results
```

#### 5.3.2 用户体验优化（第13-14周）

**目标**：提升界面交互和用户体验

**优化内容**：
- 搜索建议和自动补全
- 结果预览和快速操作
- 个性化设置和偏好
- 搜索历史和收藏功能

#### 5.3.3 系统集成测试（第14-15周）

**目标**：全面系统测试和质量保证

**测试范围**：
- 功能测试：各模块功能验证
- 性能测试：响应时间和吞吐量
- 压力测试：大数据量处理能力
- 兼容性测试：多平台兼容性

---

## 6. 后期发展计划

### 6.1 技术演进路线图

```mermaid
timeline
    title 本地AI检索系统技术演进路线图

    section 第一阶段 MVP版本
        2024-01 : 基础架构搭建
               : 元数据索引系统
               : 全文检索引擎
               : 简单Web界面

        2024-02 : 文档解析器
               : SQLite数据库
               : FastAPI后端
               : Streamlit前端

    section 第二阶段 智能化版本
        2024-03 : BGE语义模型集成
               : FAISS向量数据库
               : 智能查询路由
               : 结果融合算法

        2024-04 : 意图识别优化
               : 个性化排序
               : 缓存系统优化
               : 性能调优

    section 第三阶段 增强版本
        2024-05 : 多模态搜索
               : OCR图像识别
               : 语音转文本
               : 视频内容分析

        2024-06 : 协作搜索功能
               : 云端同步
               : 团队共享
               : 权限管理

    section 第四阶段 企业版本
        2024-07 : 分布式架构
               : 微服务拆分
               : 容器化部署
               : 监控告警

        2024-08 : 插件生态系统
               : API开放平台
               : 第三方集成
               : 自定义扩展
```

### 6.2 功能扩展规划

#### 6.2.1 多模态搜索能力

```mermaid
graph TB
    subgraph "多模态内容处理"
        IMAGE[图像处理<br/>OCR文字识别<br/>图像特征提取] --> VISION_MODEL[视觉模型<br/>CLIP/BLIP<br/>图像理解]

        AUDIO[音频处理<br/>语音转文本<br/>音频特征提取] --> SPEECH_MODEL[语音模型<br/>Whisper<br/>语音识别]

        VIDEO[视频处理<br/>关键帧提取<br/>字幕识别] --> VIDEO_MODEL[视频模型<br/>多模态理解<br/>内容分析]
    end

    subgraph "统一向量空间"
        VISION_MODEL --> MULTIMODAL_INDEX[多模态索引<br/>统一向量空间<br/>跨模态检索]
        SPEECH_MODEL --> MULTIMODAL_INDEX
        VIDEO_MODEL --> MULTIMODAL_INDEX
    end

    subgraph "跨模态搜索"
        TEXT_QUERY[文本查询<br/>"找包含图表的文档"] --> CROSS_MODAL[跨模态检索<br/>文本→图像<br/>图像→文本]
        IMAGE_QUERY[图像查询<br/>以图搜图] --> CROSS_MODAL

        CROSS_MODAL --> MULTIMODAL_RESULTS[多模态结果<br/>文档+图像+音频<br/>统一展示]
    end

    MULTIMODAL_INDEX --> CROSS_MODAL

    style MULTIMODAL_INDEX fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style CROSS_MODAL fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
```

**技术实现计划**：

| 模态类型 | 技术方案 | 开发周期 | 难度评估 |
|----------|----------|----------|----------|
| **图像搜索** | OCR + CLIP模型 | 4周 | 中等 |
| **音频搜索** | Whisper + 语义向量 | 6周 | 较高 |
| **视频搜索** | 关键帧 + 字幕提取 | 8周 | 高 |
| **跨模态检索** | 统一向量空间 | 10周 | 很高 |

#### 6.2.2 协作搜索功能

```mermaid
graph TB
    subgraph "团队协作层"
        USER_MGMT[用户管理<br/>身份认证<br/>权限控制] --> TEAM_SPACE[团队空间<br/>共享索引<br/>协作搜索]

        TEAM_SPACE --> SHARE_SEARCH[搜索分享<br/>查询共享<br/>结果标注]

        SHARE_SEARCH --> COLLAB_FILTER[协作过滤<br/>团队偏好<br/>推荐算法]
    end

    subgraph "云端同步层"
        LOCAL_INDEX[本地索引] --> SYNC_ENGINE[同步引擎<br/>增量同步<br/>冲突解决]

        SYNC_ENGINE --> CLOUD_STORAGE[云端存储<br/>索引备份<br/>跨设备同步]

        CLOUD_STORAGE --> DISTRIBUTED_SEARCH[分布式搜索<br/>多节点查询<br/>结果聚合]
    end

    subgraph "智能推荐层"
        BEHAVIOR_ANALYSIS[行为分析<br/>搜索模式<br/>使用习惯] --> PERSONAL_MODEL[个人模型<br/>偏好学习<br/>个性化排序]

        PERSONAL_MODEL --> SMART_SUGGEST[智能建议<br/>查询推荐<br/>内容发现]
    end

    TEAM_SPACE --> SYNC_ENGINE
    COLLAB_FILTER --> BEHAVIOR_ANALYSIS
    DISTRIBUTED_SEARCH --> SMART_SUGGEST

    style TEAM_SPACE fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style SYNC_ENGINE fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style SMART_SUGGEST fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

#### 6.2.3 企业级功能

**安全与合规**：
- 数据加密存储和传输
- 审计日志和操作追踪
- 合规性检查和报告
- 敏感信息识别和保护

**性能与扩展**：
- 分布式索引和搜索
- 负载均衡和故障转移
- 自动扩缩容
- 性能监控和优化

**管理与运维**：
- 集中化配置管理
- 自动化部署和更新
- 监控告警系统
- 备份和恢复策略

### 6.3 技术债务管理

#### 6.3.1 代码质量提升

```python
# 代码质量检查工具配置
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.pylint]
max-line-length = 88
disable = ["C0114", "C0116"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
```

#### 6.3.2 测试覆盖率提升

```python
# 测试策略规划
class TestStrategy:
    """测试策略管理"""

    def __init__(self):
        self.coverage_targets = {
            'unit_tests': 90,      # 单元测试覆盖率目标
            'integration_tests': 80, # 集成测试覆盖率目标
            'e2e_tests': 70        # 端到端测试覆盖率目标
        }

    def generate_test_plan(self):
        """生成测试计划"""
        return {
            'phase1': ['core_indexing', 'basic_search'],
            'phase2': ['semantic_search', 'query_routing'],
            'phase3': ['result_fusion', 'performance_optimization']
        }
```

### 6.4 开源社区建设

#### 6.4.1 开源发布计划

```mermaid
gantt
    title 开源发布时间线
    dateFormat  YYYY-MM-DD

    section 准备阶段
    代码清理            :prep1, 2024-06-01, 14d
    文档编写            :prep2, after prep1, 21d
    许可证选择          :prep3, after prep2, 7d

    section 发布阶段
    GitHub仓库创建      :release1, after prep3, 3d
    首次发布           :release2, after release1, 7d
    社区推广           :release3, after release2, 30d

    section 维护阶段
    Issue处理          :maintain1, after release3, 90d
    功能迭代           :maintain2, after maintain1, 180d
    社区贡献           :maintain3, after maintain2, 365d
```

#### 6.4.2 贡献者指南

**贡献类型**：
- 代码贡献：新功能开发、Bug修复
- 文档贡献：使用指南、API文档
- 测试贡献：测试用例、性能测试
- 设计贡献：UI/UX设计、架构设计

**贡献流程**：
1. Fork项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 代码审查和合并

### 6.5 商业化路径

#### 6.5.1 产品定位

```mermaid
quadrantChart
    title 产品定位矩阵
    x-axis 技术复杂度 --> 高
    y-axis 市场需求 --> 高

    quadrant-1 明星产品
    quadrant-2 问题产品
    quadrant-3 瘦狗产品
    quadrant-4 金牛产品

    个人版: [0.3, 0.8]
    专业版: [0.6, 0.9]
    企业版: [0.9, 0.7]
    云端版: [0.8, 0.6]
```

#### 6.5.2 收入模式

| 版本类型 | 目标用户 | 核心功能 | 定价策略 |
|----------|----------|----------|----------|
| **社区版** | 个人用户 | 基础搜索功能 | 免费开源 |
| **专业版** | 专业用户 | 高级AI功能 | 订阅制 $9.99/月 |
| **企业版** | 企业客户 | 团队协作+管理 | 按席位收费 $29.99/用户/月 |
| **云端版** | 云端用户 | SaaS服务 | 按使用量收费 |

### 6.6 风险评估与应对

#### 6.6.1 技术风险

| 风险类型 | 风险等级 | 影响程度 | 应对策略 |
|----------|----------|----------|----------|
| **AI模型性能** | 中等 | 高 | 多模型备选方案 |
| **扩展性瓶颈** | 高 | 高 | 分布式架构设计 |
| **兼容性问题** | 中等 | 中等 | 多平台测试 |
| **安全漏洞** | 高 | 高 | 安全审计和加固 |

#### 6.6.2 市场风险

| 风险类型 | 风险等级 | 影响程度 | 应对策略 |
|----------|----------|----------|----------|
| **竞争加剧** | 高 | 高 | 差异化定位 |
| **技术变革** | 中等 | 高 | 持续技术跟踪 |
| **用户接受度** | 中等 | 中等 | 用户教育和推广 |
| **法规变化** | 低 | 中等 | 合规性监控 |

### 6.7 成功指标定义

#### 6.7.1 技术指标

```python
class SuccessMetrics:
    """成功指标定义"""

    def __init__(self):
        self.technical_kpis = {
            'search_response_time': {'target': '<100ms', 'current': 0},
            'indexing_speed': {'target': '>1000files/min', 'current': 0},
            'accuracy_rate': {'target': '>95%', 'current': 0},
            'system_uptime': {'target': '>99.9%', 'current': 0}
        }

        self.business_kpis = {
            'user_adoption': {'target': '10000+', 'current': 0},
            'user_satisfaction': {'target': '>4.5/5', 'current': 0},
            'retention_rate': {'target': '>80%', 'current': 0},
            'revenue_growth': {'target': '100%YoY', 'current': 0}
        }

    def track_progress(self):
        """跟踪进度"""
        # 实现指标跟踪逻辑
        pass
```

#### 6.7.2 里程碑规划

```mermaid
timeline
    title 项目里程碑时间线

    section 2024 Q1
        MVP发布 : 基础功能完成
               : 1000+用户注册
               : 社区版发布

    section 2024 Q2
        智能化版本 : AI功能集成
                  : 5000+活跃用户
                  : 专业版发布

    section 2024 Q3
        企业版本 : 团队协作功能
               : 100+企业客户
               : 收入突破$10K/月

    section 2024 Q4
        生态建设 : 插件平台上线
               : 开发者社区建立
               : 年收入突破$100K
```

---

## 7. 总结与展望

### 7.1 项目价值总结

本地AI检索系统作为下一代文件搜索解决方案，具有以下核心价值：

**技术创新价值**：
- 融合传统搜索的高性能与AI的智能理解
- 创新的多引擎并行架构设计
- 本地化AI部署保护用户隐私

**用户体验价值**：
- 零配置开箱即用的简洁体验
- 自然语言交互降低使用门槛
- 毫秒级响应提供极致性能

**商业应用价值**：
- 个人效率工具市场的差异化产品
- 企业知识管理的创新解决方案
- 开源社区建设的技术影响力

### 7.2 技术发展趋势

**AI技术演进**：
- 大语言模型的持续优化
- 多模态AI的快速发展
- 边缘计算的普及应用

**搜索技术变革**：
- 从关键词搜索到语义搜索
- 从单一模态到多模态融合
- 从被动搜索到主动推荐

**用户需求变化**：
- 对隐私保护的更高要求
- 对智能化体验的期待提升
- 对跨平台协作的需求增长

### 7.3 项目展望

通过分阶段的技术实现和持续的产品迭代，本地AI检索系统将成为：

1. **技术标杆**：引领本地搜索技术的发展方向
2. **用户首选**：成为用户日常工作的必备工具
3. **行业影响**：推动整个搜索行业的技术进步
4. **商业成功**：建立可持续的商业模式和生态系统

这个项目不仅是技术创新的体现，更是对用户需求的深度理解和满足，将为本地文件搜索领域带来革命性的变化。

---

## 附录

### A. 技术选型对比

#### A.1 向量数据库对比

| 数据库 | 优势 | 劣势 | 适用场景 |
|--------|------|------|----------|
| **FAISS** | 高性能、本地部署、Facebook开源 | 功能相对简单、缺乏分布式支持 | 单机高性能场景 |
| **Milvus** | 分布式、云原生、功能丰富 | 部署复杂、资源占用高 | 大规模分布式场景 |
| **Weaviate** | GraphQL API、易于集成 | 性能一般、社区较小 | 快速原型开发 |
| **ChromaDB** | 轻量级、易于使用 | 性能有限、功能简单 | 小规模应用 |

#### A.2 文本嵌入模型对比

| 模型 | 语言支持 | 维度 | 性能 | 部署难度 |
|------|----------|------|------|----------|
| **BGE-large-zh** | 中文优化 | 1024 | 优秀 | 简单 |
| **text-embedding-ada-002** | 多语言 | 1536 | 优秀 | 需要API |
| **sentence-transformers** | 多语言 | 768 | 良好 | 简单 |
| **m3e-base** | 中文 | 768 | 良好 | 简单 |

### B. 性能基准测试

#### B.1 搜索性能测试

```python
# 性能测试脚本示例
import time
import statistics
from typing import List, Dict

class PerformanceBenchmark:
    """性能基准测试"""

    def __init__(self, search_engine):
        self.search_engine = search_engine
        self.test_queries = [
            "Python编程",
            "机器学习算法",
            "数据库设计",
            "系统架构",
            "项目管理"
        ]

    def run_latency_test(self, iterations: int = 100) -> Dict:
        """延迟测试"""
        latencies = []

        for query in self.test_queries:
            query_latencies = []

            for _ in range(iterations):
                start_time = time.perf_counter()
                results = self.search_engine.search(query)
                end_time = time.perf_counter()

                latency = (end_time - start_time) * 1000  # 转换为毫秒
                query_latencies.append(latency)

            latencies.extend(query_latencies)

        return {
            'mean_latency': statistics.mean(latencies),
            'median_latency': statistics.median(latencies),
            'p95_latency': statistics.quantiles(latencies, n=20)[18],
            'p99_latency': statistics.quantiles(latencies, n=100)[98],
            'min_latency': min(latencies),
            'max_latency': max(latencies)
        }

    def run_throughput_test(self, duration: int = 60) -> Dict:
        """吞吐量测试"""
        start_time = time.time()
        request_count = 0

        while time.time() - start_time < duration:
            for query in self.test_queries:
                self.search_engine.search(query)
                request_count += 1

        actual_duration = time.time() - start_time
        throughput = request_count / actual_duration

        return {
            'requests_per_second': throughput,
            'total_requests': request_count,
            'test_duration': actual_duration
        }
```

#### B.2 内存使用测试

```python
import psutil
import os

class MemoryProfiler:
    """内存使用分析器"""

    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.baseline_memory = self.get_memory_usage()

    def get_memory_usage(self) -> Dict:
        """获取内存使用情况"""
        memory_info = self.process.memory_info()
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'percent': self.process.memory_percent()
        }

    def profile_indexing(self, indexer, file_paths: List[str]):
        """分析索引过程的内存使用"""
        memory_snapshots = []

        for i, file_path in enumerate(file_paths):
            indexer.index_file(file_path)

            if i % 100 == 0:  # 每100个文件记录一次
                memory_usage = self.get_memory_usage()
                memory_snapshots.append({
                    'file_count': i + 1,
                    'memory_usage': memory_usage
                })

        return memory_snapshots
```

### C. 部署配置模板

#### C.1 Docker配置

```dockerfile
# Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建数据目录
RUN mkdir -p /app/data/indexes /app/data/cache /app/data/logs

# 暴露端口
EXPOSE 8000 8501

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "scripts/start_services.py"]
```

#### C.2 Kubernetes配置

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-search-system
  labels:
    app: ai-search-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-search-system
  template:
    metadata:
      labels:
        app: ai-search-system
    spec:
      containers:
      - name: ai-search
        image: ai-search-system:latest
        ports:
        - containerPort: 8000
        - containerPort: 8501
        env:
        - name: DATABASE_URL
          value: "sqlite:///data/search.db"
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: ai-search-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: ai-search-service
spec:
  selector:
    app: ai-search-system
  ports:
  - name: api
    port: 8000
    targetPort: 8000
  - name: web
    port: 8501
    targetPort: 8501
  type: LoadBalancer
```

### D. 监控和日志配置

#### D.1 Prometheus监控配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-search-system'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### D.2 日志配置

```python
# logging_config.py
import logging
import logging.config

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
        },
    },
    'handlers': {
        'default': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.StreamHandler',
        },
        'file': {
            'level': 'DEBUG',
            'formatter': 'detailed',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/ai_search.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
        },
        'error_file': {
            'level': 'ERROR',
            'formatter': 'detailed',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/errors.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
        },
    },
    'loggers': {
        '': {
            'handlers': ['default', 'file'],
            'level': 'DEBUG',
            'propagate': False
        },
        'ai_search.errors': {
            'handlers': ['error_file'],
            'level': 'ERROR',
            'propagate': False
        },
    }
}

def setup_logging():
    """设置日志配置"""
    logging.config.dictConfig(LOGGING_CONFIG)
```

### E. 参考资料

#### E.1 技术文档

1. **FastAPI官方文档**: https://fastapi.tiangolo.com/
2. **Streamlit官方文档**: https://docs.streamlit.io/
3. **FAISS文档**: https://faiss.ai/
4. **BGE模型**: https://huggingface.co/BAAI/bge-large-zh-v1.5
5. **Whoosh文档**: https://whoosh.readthedocs.io/

#### E.2 学术论文

1. Robertson, S. E., & Zaragoza, H. (2009). The probabilistic relevance framework: BM25 and beyond.
2. Karpukhin, V., et al. (2020). Dense Passage Retrieval for Open-Domain Question Answering.
3. Reimers, N., & Gurevych, I. (2019). Sentence-BERT: Sentence Embeddings using Siamese BERT-Networks.

#### E.3 开源项目

1. **Haystack**: https://github.com/deepset-ai/haystack
2. **LangChain**: https://github.com/hwchase17/langchain
3. **Chroma**: https://github.com/chroma-core/chroma
4. **Weaviate**: https://github.com/weaviate/weaviate

---

*本技术方案文档版本：v2.0*
*最后更新时间：2024年1月*
*文档维护：AI检索系统开发团队*
