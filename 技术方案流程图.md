          ┌───────────────────────────────────────┐
          │                用户界面(UI)            │
          │  - 即时搜索框（文件名/内容/自然语言）  │
          │  - 高级搜索条件（类型/时间/大小等）    │
          │  - AI 问答窗口                         │
          └───────────────────────┬───────────────┘
                                  │
                          ┌───────▼─────────┐
                          │   搜索调度中心   │
                          │ (Query Router)  │
                          │-----------------│
                          │ 识别用户输入类型 │
                          │ - 短关键词 → 元数据索引 │
                          │ - 内容关键词 → 全文索引 │
                          │ - 自然语言 → 语义索引   │
                          └───────┬─────────┘
                  ┌──────────────┼──────────────┐
                  │              │              │
      ┌───────────▼───────┐ ┌────▼───────────┐ ┌──────────▼─────────┐
      │ 元数据索引模块     │ │ 全文索引模块   │ │ 语义索引模块       │
      │ (Everything SDK)  │ │ (倒排索引)     │ │ (Embedding + Faiss)│
      │ 文件名/路径/时间   │ │ 文件内容关键词 │ │ 向量相似度搜索     │
      └───────────┬───────┘ └────┬───────────┘ └──────────┬─────────┘
                  │              │                        │
          ┌───────▼──────────┐ ┌─▼───────────────────┐   │
          │ 文件系统监听模块 │ │ 文档解析模块         │   │
          │ (实时更新索引)  │ │ PDF/Office/TXT/OCR   │   │
          └─────────────────┘ └─────────────────────┘   │
                                                          │
                                    ┌─────────────────────▼───────────────────┐
                                    │              AI 问答引擎                 │
                                    │ (RAG: 检索增强生成)                       │
                                    │ - 调用语义索引找到相关片段               │
                                    │ - 本地/云端大模型生成回答                 │
                                    └──────────────────────────────────────────┘

#1.用户意图判断
  - 用户意图识别，路由分发，搜索调度
  - 如果是短关键词（如 合同2023）→ 走 元数据索引（最快）
  - 如果是“找正文包含 xxx 的文档” → 走 全文索引
  - 如果是自然语言（如“找我去年发给张三的合同”）→ 走 语义索引

#2.元数据索引：
    - 必须支持**全部文件类型**（不限格式），基于文件名、路径、标签进行检索
    - 索引速度接近 Everything（高性能文件系统扫描）
    - 支持实时索引更新（新增、修改、删除文件）
#3.全文检索：
    - 针对常用文档类型（PDF、Office 三件套：docx/xlsx/pptx、TXT、Markdown）
    - 使用 Whoosh 实现 BM25 关键词检索
    - 文本提取：
        * PDF：pdfminer 或 pypdf
        * Office：python-docx、openpyxl、python-pptx
        * TXT、MD：直接读取
    - 支持增量更新全文索引

#4.语义检索：
    - 前期支持的文件类型：PDF、Office 三件套、TXT、Markdown
    - 后期扩展支持：图片（OCR + 图像向量化）、音频（语音转文本 + 向量化）、视频（关键帧提取 + 向量化）
    - 使用 bge 模型（中文向量化）
    - 支持 Milvus / Weaviate / FAISS 三种向量数据库（通过 config.yaml 切换）
    - 文档摘要向量化策略：
        * 对全文生成 200~500 字的中文摘要
        * 摘要向量化存储
        * 重要段落单独向量化（可选）
#5.混合检索：
    - 将元数据检索、全文检索、语义检索结果融合
    - 使用加权 BM25 分数 + 向量相似度进行综合排序
    - 权重可在配置文件调整

#6.
1. 索引初始化：扫描文件夹 → 提取元数据+文本 → 建立元数据索引+全文索引+向量索引
  - 元数据索引：直接从文件系统扫描元数据（Everything API/Fsearch）
  - 全文索引：解析文档 → 分词 → 建立倒排索引（Lucene/Whoosh）
  - 语义索引：解析文档 →文本分块 (Chunking) → 向量化（Embedding）→ 存入向量数据库（Faiss/Milvus）
2. 实时更新：文件系统监视器 (File System Monitor):（inotify/Everything API）

#7.语义检索在百万文件是否合适？只提取文档的摘要生成